"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { Separator } from "./ui/separator";
import { ScrollArea } from "./ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "./ui/dropdown-menu";
import { PlusCircle, Settings, HelpCircle, User } from "lucide-react";
import { useChat } from "../contexts/ChatContext";

export default function LeftSidebar() {
  const { conversations, createConversation, loadConversation, currentConversation, fetchConversations } = useChat();
  const [isCreating, setIsCreating] = useState(false);

  // Fetch conversations when component mounts
  useEffect(() => {
    // Only fetch if we don't already have conversations
    if (conversations.length === 0) {
      fetchConversations();
    }
  }, [fetchConversations, conversations.length]);

  // <PERSON>le creating a new conversation
  const handleNewChat = async () => {
    try {
      setIsCreating(true);
      const newConversationId = await createConversation();
      await loadConversation(newConversationId);
    } catch (error) {
      console.error('Error creating new conversation:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle loading an existing conversation
  const handleLoadConversation = async (id: string) => {
    try {
      await loadConversation(id);
    } catch (error) {
      console.error('Error loading conversation:', error);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | Date) => {
    const date = dateString instanceof Date ? dateString : new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  return (
    <div className="w-64 h-full border-r bg-muted/10 flex flex-col">
      {/* App Name/Logo */}
      <div className="p-4">
        <h1 className="text-xl font-bold">ApplyMate 🤖</h1>
      </div>

      <Separator />

      {/* New Chat Button */}
      <div className="p-4">
        <Button
          className="w-full"
          onClick={handleNewChat}
          disabled={isCreating}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          New Chat
        </Button>
      </div>

      {/* Chat History */}
      <ScrollArea className="flex-1 px-4">
        <div className="space-y-2 py-2">
          <h2 className="text-sm font-semibold mb-2">Recent Chats</h2>
          {conversations.length === 0 ? (
            <div className="text-sm text-muted-foreground py-2">
              No conversations yet. Start a new chat!
            </div>
          ) : (
            conversations.map((chat) => (
              <Button
                key={chat._id}
                variant={currentConversation?._id === chat._id ? "secondary" : "ghost"}
                className="w-full justify-start text-left h-auto py-2"
                onClick={() => handleLoadConversation(chat._id)}
              >
                <div className="flex flex-col items-start">
                  <span className="font-medium">{chat.title}</span>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(chat.lastMessageAt || chat.createdAt)}
                  </span>
                </div>
              </Button>
            ))
          )}
        </div>
      </ScrollArea>

      <Separator />

      {/* Bottom Navigation */}
      <div className="p-4 flex justify-between">
        <Button variant="ghost" size="icon">
          <User className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <HelpCircle className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Documentation</DropdownMenuItem>
            <DropdownMenuItem>Keyboard Shortcuts</DropdownMenuItem>
            <DropdownMenuItem>About</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
