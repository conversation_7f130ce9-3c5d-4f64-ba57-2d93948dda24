import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import connectToDatabase from '@/lib/mongodb';
import Conversation from '@/models/Conversation';

// Get all conversations for the current user
export async function GET(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectToDatabase();

    // Get conversations sorted by last message time
    const conversations = await Conversation.find({ userId })
      .sort({ lastMessageAt: -1 })
      .select('title lastMessageAt createdAt')
      .lean();

    return NextResponse.json({ conversations });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversations' },
      { status: 500 }
    );
  }
}

// Create a new conversation
export async function POST(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { title, jobApplicationId, initialMessage } = await req.json();

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Create initial messages array with system message
    const messages = [];
    
    // Add initial user message if provided
    if (initialMessage) {
      messages.push({
        id: '1',
        role: 'user',
        content: initialMessage,
        timestamp: new Date(),
      });
    }

    // Create the conversation
    const conversation = new Conversation({
      userId,
      title,
      messages,
      jobApplicationId: jobApplicationId || undefined,
    });

    await conversation.save();

    return NextResponse.json({ conversation }, { status: 201 });
  } catch (error) {
    console.error('Error creating conversation:', error);
    return NextResponse.json(
      { error: 'Failed to create conversation' },
      { status: 500 }
    );
  }
}
