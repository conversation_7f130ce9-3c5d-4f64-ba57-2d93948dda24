"use client";

import React from 'react';
import { AuthProvider } from '../contexts/AuthContext';
import { ChatProvider } from '../contexts/ChatContext';
import { ProfileProvider } from '../contexts/ProfileContext';
import { ThemeProvider } from './theme-provider';

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="system" storageKey="theme">
      <AuthProvider>
        <ChatProvider>
          <ProfileProvider>
            {children}
          </ProfileProvider>
        </ChatProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
