import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import { generateInterviewPrep } from '@/lib/groq';

export async function POST(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { jobTitle, resumeText, jobDescription } = await req.json();

    if (!jobTitle) {
      return NextResponse.json(
        { error: 'Job title is required' },
        { status: 400 }
      );
    }

    // Generate interview preparation
    const response = await generateInterviewPrep(jobTitle, resumeText, jobDescription);

    return NextResponse.json({
      interviewPrep: response.content,
    });
  } catch (error) {
    console.error('Error generating interview preparation:', error);
    return NextResponse.json(
      { error: 'Failed to generate interview preparation' },
      { status: 500 }
    );
  }
}
