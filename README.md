# ApplyMate - AI Job Application Assistant

ApplyMate is an AI-powered chat application designed to help students and fresh graduates with job applications, resume building, cover letters, and interview preparation.

## Features

- **AI-Powered Chat**: Engage in conversations with an AI assistant specialized in job applications
- **Resume Management**: Create, store, and get feedback on your resumes
- **Cover Letter Generation**: Generate customized cover letters for specific job descriptions
- **Interview Preparation**: Practice with AI-generated interview questions and get feedback
- **Job Application Tracking**: Keep track of your job applications and their statuses

## Tech Stack

- **Frontend**: Next.js, React, TailwindCSS, ShadCN UI
- **Backend**: Next.js API Routes
- **Database**: MongoDB
- **AI**: Groq AI (Llama 3 70B model)
- **Authentication**: JWT-based authentication

## Getting Started

### Prerequisites

- Node.js 18.x or later
- MongoDB Atlas account or local MongoDB instance
- Groq API key

### Environment Setup

1. Clone the repository
2. Copy `.env.local.example` to `.env.local` and fill in the required values:
   - `MONGODB_URI`: Your MongoDB connection string
   - `GROQ_API_KEY`: Your Groq API key
   - `JWT_SECRET`: A secure random string for JWT token generation

### Installation

```bash
# Install dependencies
npm install

# Run the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Project Structure

- `/app`: Next.js app router pages and API routes
- `/components`: React components
- `/contexts`: React context providers for state management
- `/lib`: Utility functions and API clients
- `/models`: MongoDB schema definitions
- `/public`: Static assets

## API Routes

- `/api/auth`: Authentication endpoints (register, login)
- `/api/conversations`: Chat conversation management
- `/api/resumes`: Resume management
- `/api/job-applications`: Job application tracking
- `/api/ai`: Specialized AI endpoints for specific tasks

## Deployment

The easiest way to deploy this application is to use the [Vercel Platform](https://vercel.com/new) from the creators of Next.js.

Make sure to add the required environment variables in your Vercel project settings.
