"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { useProfile } from "../contexts/ProfileContext";
import { FileText, Mail, Phone, Calendar, Building, GraduationCap } from "lucide-react";

interface ResumeDetailsProps {
  resumeId?: string;
}

export default function ResumeDetails({ resumeId }: ResumeDetailsProps) {
  const {
    resumes,
    currentResume,
    loadResume,
    updateResume,
  } = useProfile();
  const [isEditing, setIsEditing] = useState(false);

  // Load the resume if resumeId is provided and not already loaded
  useEffect(() => {
    if (resumeId && (!currentResume || currentResume._id !== resumeId)) {
      loadResume(resumeId);
    }
  }, [resumeId, currentResume, loadResume]);

  // If no resume is loaded, show a message
  if (!currentResume) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No Resume Selected</h3>
        <p className="text-sm text-muted-foreground text-center mt-2">
          Select a resume from your list or create a new one to get started.
        </p>
        <Button className="mt-4">
          Create New Resume
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">Resume Summary</h3>
        <p className="text-sm text-muted-foreground">
          Upload or edit your resume to help the AI provide better assistance
        </p>
      </div>

      <div className="border rounded-lg p-4">
        <div className="text-sm">
          <p className="font-medium">{currentResume.fullName}</p>
          <p className="text-muted-foreground">{currentResume.title}</p>
          
          <div className="flex items-center text-muted-foreground text-xs mt-1">
            <Mail className="h-3 w-3 mr-1" />
            <p>{currentResume.email}</p>
            {currentResume.phone && (
              <>
                <span className="mx-1">|</span>
                <Phone className="h-3 w-3 mr-1" />
                <p>{currentResume.phone}</p>
              </>
            )}
          </div>

          {currentResume.summary && (
            <div className="mt-3">
              <p className="text-xs">{currentResume.summary}</p>
            </div>
          )}

          {currentResume.skills && currentResume.skills.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-xs">SKILLS</p>
              <p className="mt-1">{currentResume.skills.join(', ')}</p>
            </div>
          )}

          {currentResume.experience && currentResume.experience.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-xs">EXPERIENCE</p>
              {currentResume.experience.map((exp, index) => (
                <div key={index} className="mt-2">
                  <p className="font-medium">{exp.title} at {exp.company}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    <p>
                      {new Date(exp.startDate).toLocaleDateString()} - 
                      {exp.current ? 'Present' : new Date(exp.endDate as string).toLocaleDateString()}
                    </p>
                    {exp.location && (
                      <>
                        <span className="mx-1">|</span>
                        <Building className="h-3 w-3 mr-1" />
                        <p>{exp.location}</p>
                      </>
                    )}
                  </div>
                  {exp.description && (
                    <p className="text-xs mt-1">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}

          {currentResume.education && currentResume.education.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-xs">EDUCATION</p>
              {currentResume.education.map((edu, index) => (
                <div key={index} className="mt-2">
                  <p className="font-medium">{edu.degree}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <GraduationCap className="h-3 w-3 mr-1" />
                    <p>{edu.institution}</p>
                    {edu.location && (
                      <>
                        <span className="mx-1">|</span>
                        <Building className="h-3 w-3 mr-1" />
                        <p>{edu.location}</p>
                      </>
                    )}
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground mt-1">
                    <Calendar className="h-3 w-3 mr-1" />
                    <p>Graduated: {new Date(edu.graduationDate).toLocaleDateString()}</p>
                  </div>
                  {edu.description && (
                    <p className="text-xs mt-1">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <Button variant="outline" size="sm" className="mr-2">
          Upload New
        </Button>
        <Button 
          size="sm"
          onClick={() => setIsEditing(true)}
        >
          Edit Resume
        </Button>
      </div>
    </div>
  );
}