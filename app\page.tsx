"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import ChatLayout from "../components/chat-layout";
import { useAuth } from "../contexts/AuthContext";

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the chat layout if authenticated
  return isAuthenticated ? (
    <div className="min-h-screen">
      <ChatLayout />
    </div>
  ) : null;
}
