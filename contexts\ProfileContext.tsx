"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

interface Experience {
  title: string;
  company: string;
  location: string;
  startDate: Date | string;
  endDate: Date | string | null;
  current: boolean;
  description: string;
}

interface Education {
  degree: string;
  institution: string;
  location: string;
  graduationDate: Date | string;
  description: string;
}

interface Resume {
  _id: string;
  fullName: string;
  email: string;
  phone: string;
  title: string;
  summary: string;
  skills: string[];
  experience: Experience[];
  education: Education[];
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface JobApplication {
  _id: string;
  title: string;
  company: string;
  location: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  status: 'saved' | 'applied' | 'interviewing' | 'offered' | 'rejected';
  notes: string;
  applicationDate: Date | string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface ProfileContextType {
  resumes: Resume[];
  currentResume: Resume | null;
  jobApplications: JobApplication[];
  currentJobApplication: JobApplication | null;
  isLoading: boolean;
  fetchResumes: () => Promise<void>;
  fetchJobApplications: () => Promise<void>;
  createResume: (resumeData: Partial<Resume>) => Promise<string>;
  updateResume: (id: string, resumeData: Partial<Resume>) => Promise<void>;
  deleteResume: (id: string) => Promise<void>;
  loadResume: (id: string) => Promise<void>;
  createJobApplication: (jobData: Partial<JobApplication>) => Promise<string>;
  updateJobApplication: (id: string, jobData: Partial<JobApplication>) => Promise<void>;
  deleteJobApplication: (id: string) => Promise<void>;
  loadJobApplication: (id: string) => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuth();
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [currentResume, setCurrentResume] = useState<Resume | null>(null);
  const [jobApplications, setJobApplications] = useState<JobApplication[]>([]);
  const [currentJobApplication, setCurrentJobApplication] = useState<JobApplication | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchResumes();
      fetchJobApplications();
    } else {
      setResumes([]);
      setCurrentResume(null);
      setJobApplications([]);
      setCurrentJobApplication(null);
    }
  }, [isAuthenticated]);

  // Resume functions
  const fetchResumes = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('/api/resumes');
      setResumes(response.data.resumes);
    } catch (error) {
      console.error('Error fetching resumes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createResume = async (resumeData: Partial<Resume>) => {
    try {
      setIsLoading(true);
      const response = await axios.post('/api/resumes', resumeData);
      
      // Add to resumes list
      const newResume = response.data.resume;
      setResumes(prev => [newResume, ...prev]);
      
      // Return the ID
      return newResume._id;
    } catch (error) {
      console.error('Error creating resume:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateResume = async (id: string, resumeData: Partial<Resume>) => {
    try {
      setIsLoading(true);
      const response = await axios.put(`/api/resumes/${id}`, resumeData);
      
      // Update in resumes list
      const updatedResume = response.data.resume;
      setResumes(prev => 
        prev.map(resume => 
          resume._id === id ? updatedResume : resume
        )
      );
      
      // Update current resume if it was updated
      if (currentResume && currentResume._id === id) {
        setCurrentResume(updatedResume);
      }
    } catch (error) {
      console.error('Error updating resume:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteResume = async (id: string) => {
    try {
      await axios.delete(`/api/resumes/${id}`);
      
      // Remove from resumes list
      setResumes(prev => prev.filter(resume => resume._id !== id));
      
      // Clear current resume if it was deleted
      if (currentResume && currentResume._id === id) {
        setCurrentResume(null);
      }
    } catch (error) {
      console.error('Error deleting resume:', error);
      throw error;
    }
  };

  const loadResume = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await axios.get(`/api/resumes/${id}`);
      setCurrentResume(response.data.resume);
    } catch (error) {
      console.error('Error loading resume:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Job application functions
  const fetchJobApplications = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('/api/job-applications');
      setJobApplications(response.data.jobApplications);
    } catch (error) {
      console.error('Error fetching job applications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createJobApplication = async (jobData: Partial<JobApplication>) => {
    try {
      setIsLoading(true);
      const response = await axios.post('/api/job-applications', jobData);
      
      // Add to job applications list
      const newJobApplication = response.data.jobApplication;
      setJobApplications(prev => [newJobApplication, ...prev]);
      
      // Return the ID
      return newJobApplication._id;
    } catch (error) {
      console.error('Error creating job application:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateJobApplication = async (id: string, jobData: Partial<JobApplication>) => {
    try {
      setIsLoading(true);
      const response = await axios.put(`/api/job-applications/${id}`, jobData);
      
      // Update in job applications list
      const updatedJobApplication = response.data.jobApplication;
      setJobApplications(prev => 
        prev.map(job => 
          job._id === id ? updatedJobApplication : job
        )
      );
      
      // Update current job application if it was updated
      if (currentJobApplication && currentJobApplication._id === id) {
        setCurrentJobApplication(updatedJobApplication);
      }
    } catch (error) {
      console.error('Error updating job application:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteJobApplication = async (id: string) => {
    try {
      await axios.delete(`/api/job-applications/${id}`);
      
      // Remove from job applications list
      setJobApplications(prev => prev.filter(job => job._id !== id));
      
      // Clear current job application if it was deleted
      if (currentJobApplication && currentJobApplication._id === id) {
        setCurrentJobApplication(null);
      }
    } catch (error) {
      console.error('Error deleting job application:', error);
      throw error;
    }
  };

  const loadJobApplication = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await axios.get(`/api/job-applications/${id}`);
      setCurrentJobApplication(response.data.jobApplication);
    } catch (error) {
      console.error('Error loading job application:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    resumes,
    currentResume,
    jobApplications,
    currentJobApplication,
    isLoading,
    fetchResumes,
    fetchJobApplications,
    createResume,
    updateResume,
    deleteResume,
    loadResume,
    createJobApplication,
    updateJobApplication,
    deleteJobApplication,
    loadJobApplication,
  };

  return <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>;
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}
