import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import connectToDatabase from '@/lib/mongodb';
import JobApplication from '@/models/JobApplication';
import mongoose from 'mongoose';

// Get a specific job application by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    
    // Validate job application ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid job application ID' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Find the job application
    const jobApplication = await JobApplication.findOne({
      _id: id,
      userId,
    });

    if (!jobApplication) {
      return NextResponse.json(
        { error: 'Job application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ jobApplication });
  } catch (error) {
    console.error('Error fetching job application:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job application' },
      { status: 500 }
    );
  }
}

// Update a job application
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const jobData = await req.json();
    
    // Validate job application ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid job application ID' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!jobData.title || !jobData.company) {
      return NextResponse.json(
        { error: 'Job title and company are required' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Find and update the job application
    const jobApplication = await JobApplication.findOneAndUpdate(
      { _id: id, userId },
      jobData,
      { new: true }
    );

    if (!jobApplication) {
      return NextResponse.json(
        { error: 'Job application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ jobApplication });
  } catch (error) {
    console.error('Error updating job application:', error);
    return NextResponse.json(
      { error: 'Failed to update job application' },
      { status: 500 }
    );
  }
}

// Delete a job application
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    
    // Validate job application ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid job application ID' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Find and delete the job application
    const result = await JobApplication.findOneAndDelete({
      _id: id,
      userId,
    });

    if (!result) {
      return NextResponse.json(
        { error: 'Job application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Job application deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting job application:', error);
    return NextResponse.json(
      { error: 'Failed to delete job application' },
      { status: 500 }
    );
  }
}
