"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { ScrollArea } from "./ui/scroll-area";
import { Avatar } from "./ui/avatar";
import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { Send, FileUp, Sparkles, Clipboard, ThumbsUp, ThumbsDown, MoreHorizontal, PanelRight } from "lucide-react";
import { useChat } from "../contexts/ChatContext";

// Initial welcome message
const welcomeMessage = {
  id: "welcome",
  sender: "ai",
  content: "👋 Hi there! I'm App<PERSON><PERSON><PERSON>, your AI job application assistant. How can I help you today?",
  timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
};

interface ChatWindowProps {
  onOpenRightPanel: () => void;
}

export default function ChatWindow({ onOpenRightPanel }: ChatWindowProps) {
  const { currentConversation, sendMessage, isLoading, isSending } = useChat();
  const [inputValue, setInputValue] = useState("");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Format messages from the conversation for display
  const messages = currentConversation?.messages
    ? currentConversation.messages.map(msg => ({
        id: msg.id,
        sender: msg.role === 'user' ? 'user' : 'ai',
        content: msg.content,
        timestamp: new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      }))
    : [welcomeMessage];

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (inputValue.trim() && !isSending) {
      try {
        // Store the message locally before sending
        const messageToSend = inputValue;
        setInputValue("");

        // Send the message to the backend
        await sendMessage(messageToSend);
      } catch (error) {
        console.error('Error sending message:', error);
        // Could add error handling UI here
      }
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="border-b p-4 flex justify-between items-center">
        <div>
          <h2 className="font-semibold">New Chat</h2>
          <p className="text-sm text-muted-foreground">Start a conversation with ApplyMate</p>
        </div>
        <Button variant="outline" size="icon" onClick={onOpenRightPanel}>
          <PanelRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
            >
              <div className={`flex ${message.sender === "user" ? "flex-row-reverse" : "flex-row"} gap-3 max-w-[80%]`}>
                <Avatar className={`h-8 w-8 ${message.sender === "user" ? "bg-primary" : "bg-secondary"}`}>
                  <div className="flex h-full items-center justify-center text-xs font-medium">
                    {message.sender === "user" ? "You" : "AI"}
                  </div>
                </Avatar>

                <div className="space-y-1">
                  <Card className={`p-3 ${message.sender === "user" ? "bg-primary text-primary-foreground" : "bg-card"}`}>
                    <div className="text-sm">{message.content}</div>
                  </Card>

                  <div className="flex items-center text-xs text-muted-foreground">
                    <span>{message.timestamp}</span>

                    {message.sender === "ai" && (
                      <div className="flex items-center ml-2 space-x-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Helpful</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <ThumbsDown className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Not helpful</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <Clipboard className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Copy to clipboard</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Suggestion Buttons (only show after AI response) */}
      {messages.length > 1 && messages[messages.length - 1].sender === "ai" && (
        <div className="px-4 py-2 flex flex-wrap gap-2">
          <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
            Help with my resume
          </Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
            Write a cover letter
          </Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
            Interview preparation
          </Badge>
        </div>
      )}

      {/* Input Area */}
      <div className="border-t p-4">
        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <FileUp className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Upload resume or document</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <div className="flex-1 flex items-center gap-2 border rounded-md px-3 py-2">
            <Input
              className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
              placeholder="Ask me anything about your job application..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button variant="ghost" size="icon" onClick={handleSendMessage} disabled={!inputValue.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Sparkles className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Use AI suggestions</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="mt-2 text-xs text-center text-muted-foreground">
          ApplyMate may produce inaccurate information. Always verify important details.
        </div>
      </div>
    </div>
  );
}
