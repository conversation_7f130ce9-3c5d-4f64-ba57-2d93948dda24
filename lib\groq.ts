import { Groq } from 'groq-sdk';

// Initialize the Groq client with API key
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

// Default system prompt for job application assistant
const DEFAULT_SYSTEM_PROMPT = `You are Apply<PERSON>ate, an AI assistant specialized in helping students and fresh graduates with job applications, resume building, cover letters, and interview preparation. 

Your expertise includes:
- Providing tailored resume feedback and suggestions
- Creating professional cover letters customized to specific job descriptions
- Preparing users for interviews with practice questions and answers
- Helping with LinkedIn profile optimization
- Offering career advice for entry-level positions

Be friendly, supportive, and professional. Provide specific, actionable advice rather than generic statements. When appropriate, ask clarifying questions to better understand the user's needs.

Always maintain a positive and encouraging tone, as job searching can be stressful. Focus on highlighting the user's strengths while providing constructive feedback on areas for improvement.`;

// Function to generate a chat completion
export async function generateChatCompletion(
  messages: { role: 'user' | 'assistant' | 'system'; content: string }[],
  options = {}
) {
  try {
    // Add system prompt if not present
    if (!messages.some(message => message.role === 'system')) {
      messages = [
        { role: 'system', content: DEFAULT_SYSTEM_PROMPT },
        ...messages
      ];
    }

    const completion = await groq.chat.completions.create({
      messages,
      model: 'llama3-70b-8192', // Using Llama 3 70B model
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.9,
      stream: false,
      ...options
    });

    return completion.choices[0].message;
  } catch (error) {
    console.error('Error generating chat completion:', error);
    throw error;
  }
}

// Function to generate a resume review
export async function generateResumeReview(resumeText: string, jobDescription?: string) {
  const systemPrompt = `${DEFAULT_SYSTEM_PROMPT}
  
You are now reviewing a resume. Provide constructive feedback on the following aspects:
1. Overall structure and formatting
2. Content relevance and impact
3. Skills presentation
4. Experience descriptions (achievements vs responsibilities)
5. Education section
6. Specific improvement suggestions
7. ATS optimization tips

${jobDescription ? 'Compare the resume against the provided job description and suggest tailored improvements.' : ''}

Format your response with clear headings and bullet points for readability.`;

  const messages = [
    { role: 'system' as const, content: systemPrompt },
    { role: 'user' as const, content: `Please review my resume:
    
${resumeText}

${jobDescription ? `For this job description:
${jobDescription}` : ''}` }
  ];

  return generateChatCompletion(messages);
}

// Function to generate a cover letter
export async function generateCoverLetter(resumeText: string, jobDescription: string, companyInfo?: string) {
  const systemPrompt = `${DEFAULT_SYSTEM_PROMPT}
  
You are now writing a professional cover letter. Create a compelling, personalized cover letter that:
1. Addresses the specific job requirements
2. Highlights relevant skills and experiences from the resume
3. Demonstrates understanding of the company's values and needs
4. Shows enthusiasm for the position
5. Maintains a professional but engaging tone
6. Includes a strong opening and closing

The cover letter should be concise (250-350 words) and follow standard business letter format.`;

  const messages = [
    { role: 'system' as const, content: systemPrompt },
    { role: 'user' as const, content: `Please write a cover letter based on my resume:
    
Resume:
${resumeText}

Job Description:
${jobDescription}

${companyInfo ? `Company Information:
${companyInfo}` : ''}` }
  ];

  return generateChatCompletion(messages);
}

// Function to generate interview preparation
export async function generateInterviewPrep(jobTitle: string, resumeText?: string, jobDescription?: string) {
  const systemPrompt = `${DEFAULT_SYSTEM_PROMPT}
  
You are now helping prepare for a job interview. Provide:
1. 10 common interview questions for this position
2. 5 technical or role-specific questions
3. Suggested answers that incorporate the candidate's background
4. Tips for answering behavioral questions
5. Advice on questions to ask the interviewer

Format your response with clear sections and numbered questions.`;

  const messages = [
    { role: 'system' as const, content: systemPrompt },
    { role: 'user' as const, content: `Please help me prepare for my ${jobTitle} interview.
    
${resumeText ? `My resume:
${resumeText}` : ''}

${jobDescription ? `Job Description:
${jobDescription}` : ''}` }
  ];

  return generateChatCompletion(messages);
}

export default groq;
