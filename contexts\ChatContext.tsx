"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date | string;
}

interface Conversation {
  _id: string;
  title: string;
  lastMessageAt: Date | string;
  createdAt: Date | string;
}

interface ConversationDetails extends Conversation {
  messages: Message[];
}

interface ChatContextType {
  conversations: Conversation[];
  currentConversation: ConversationDetails | null;
  isLoading: boolean;
  isSending: boolean;
  fetchConversations: () => Promise<void>;
  createConversation: (title?: string, initialMessage?: string) => Promise<string>;
  loadConversation: (id: string) => Promise<void>;
  sendMessage: (content: string) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ConversationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);

  // Fetch conversations when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchConversations();
    } else {
      setConversations([]);
      setCurrentConversation(null);
    }
  }, [isAuthenticated]);

  // Fetch all conversations
  const fetchConversations = useCallback(async () => {
    // Skip API call if not authenticated
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      const response = await axios.get('/api/conversations');
      setConversations(response.data.conversations || []);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      // If we get a 401, clear the conversations
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        setConversations([]);
      }
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Create a new conversation
  const createConversation = useCallback(async (title = 'New Chat', initialMessage?: string) => {
    // Skip API call if not authenticated
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }

    try {
      setIsLoading(true);
      const response = await axios.post('/api/conversations', {
        title,
        initialMessage,
      });

      // Add to conversations list
      const newConversation = response.data.conversation;
      setConversations(prev => [newConversation, ...prev]);

      // Return the ID for navigation
      return newConversation._id;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Load a specific conversation
  const loadConversation = useCallback(async (id: string) => {
    // Skip API call if not authenticated
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }

    try {
      setIsLoading(true);
      const response = await axios.get(`/api/conversations/${id}`);
      setCurrentConversation(response.data.conversation);
    } catch (error) {
      console.error('Error loading conversation:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Send a message in the current conversation
  const sendMessage = useCallback(async (content: string) => {
    // Skip API call if not authenticated or no conversation
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }

    if (!currentConversation) {
      throw new Error('No active conversation');
    }

    try {
      setIsSending(true);
      const response = await axios.post(`/api/conversations/${currentConversation._id}/messages`, {
        content,
      });

      // Update current conversation with new messages
      setCurrentConversation(prev => {
        if (!prev) return null;

        return {
          ...prev,
          title: response.data.title || prev.title,
          messages: [
            ...prev.messages,
            response.data.userMessage,
            response.data.assistantMessage,
          ],
        };
      });

      // Update conversation list if title changed
      if (response.data.title && currentConversation.title !== response.data.title) {
        setConversations(prev =>
          prev.map(conv =>
            conv._id === currentConversation._id
              ? { ...conv, title: response.data.title }
              : conv
          )
        );
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    } finally {
      setIsSending(false);
    }
  }, [currentConversation, isAuthenticated]);

  // Delete a conversation
  const deleteConversation = useCallback(async (id: string) => {
    // Skip API call if not authenticated
    if (!isAuthenticated) {
      throw new Error('Authentication required');
    }

    try {
      await axios.delete(`/api/conversations/${id}`);

      // Remove from conversations list
      setConversations(prev => prev.filter(conv => conv._id !== id));

      // Clear current conversation if it was deleted
      if (currentConversation && currentConversation._id === id) {
        setCurrentConversation(null);
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }, [currentConversation, isAuthenticated]);

  const value = {
    conversations,
    currentConversation,
    isLoading,
    isSending,
    fetchConversations,
    createConversation,
    loadConversation,
    sendMessage,
    deleteConversation,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}
