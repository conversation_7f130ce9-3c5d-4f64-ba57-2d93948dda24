import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import connectToDatabase from '@/lib/mongodb';
import JobApplication from '@/models/JobApplication';

// Get all job applications for the current user
export async function GET(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectToDatabase();

    // Get status filter from query params
    const url = new URL(req.url);
    const status = url.searchParams.get('status');

    // Build query
    const query: any = { userId };
    if (status) {
      query.status = status;
    }

    const jobApplications = await JobApplication.find(query)
      .sort({ updatedAt: -1 })
      .lean();

    return NextResponse.json({ jobApplications });
  } catch (error) {
    console.error('Error fetching job applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job applications' },
      { status: 500 }
    );
  }
}

// Create a new job application
export async function POST(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobData = await req.json();

    // Validate required fields
    if (!jobData.title || !jobData.company) {
      return NextResponse.json(
        { error: 'Job title and company are required' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Create the job application
    const jobApplication = new JobApplication({
      userId,
      ...jobData,
    });

    await jobApplication.save();

    return NextResponse.json({ jobApplication }, { status: 201 });
  } catch (error) {
    console.error('Error creating job application:', error);
    return NextResponse.json(
      { error: 'Failed to create job application' },
      { status: 500 }
    );
  }
}
