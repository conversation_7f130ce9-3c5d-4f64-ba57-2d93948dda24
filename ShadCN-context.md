# ShadCN Components Used

This file tracks all ShadCN components used in the AI Job Chatbot project.

## Components

| Component | Status | Description | Used In |
|-----------|--------|-------------|---------|
| Button | ✅ Installed | Used for actions like sending messages and navigation | LeftSidebar, ChatWindow, RightPanel |
| Card | ✅ Installed | Used for message containers and UI panels | ChatWindow |
| Avatar | ✅ Installed | Used for user and AI profile images | ChatWindow |
| Input | ✅ Installed | Used for text input in the chat interface | ChatWindow |
| Textarea | ✅ Installed | Used for multi-line text input | Not used yet |
| Separator | ✅ Installed | Used to visually separate content sections | LeftSidebar, RightPanel |
| ScrollArea | ✅ Installed | Used for scrollable chat history | LeftSidebar, ChatWindow, RightPanel |
| Sheet | ✅ Installed | Used for slide-in panels (right panel) | RightPanel |
| DropdownMenu | ✅ Installed | Used for settings and options menus | LeftSidebar |
| Tooltip | ✅ Installed | Used for providing additional context on hover | ChatWindow |
| Tabs | ✅ Installed | Used for switching between different sections | RightPanel |
| Badge | ✅ Installed | Used for status indicators and labels | ChatWindow |
