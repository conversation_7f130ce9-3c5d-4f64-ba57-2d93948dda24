"use client";

import { useState } from "react";
import LeftSidebar from "./left-sidebar";
import Chat<PERSON><PERSON>ow from "./chat-window";
import RightPanel from "./right-panel";

export default function ChatLayout() {
  const [rightPanelOpen, setRightPanelOpen] = useState(false);

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Left Sidebar */}
      <LeftSidebar />
      
      {/* Main Chat Window */}
      <div className="flex-1 flex flex-col">
        <ChatWindow onOpenRightPanel={() => setRightPanelOpen(true)} />
      </div>
      
      {/* Right Panel (Optional Slide-in) */}
      <RightPanel open={rightPanelOpen} onClose={() => setRightPanelOpen(false)} />
    </div>
  );
}
