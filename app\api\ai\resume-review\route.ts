import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import { generateResumeReview } from '@/lib/groq';

export async function POST(req: NextRequest) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { resumeText, jobDescription } = await req.json();

    if (!resumeText) {
      return NextResponse.json(
        { error: 'Resume text is required' },
        { status: 400 }
      );
    }

    // Generate resume review
    const response = await generateResumeReview(resumeText, jobDescription);

    return NextResponse.json({
      review: response.content,
    });
  } catch (error) {
    console.error('Error generating resume review:', error);
    return NextResponse.json(
      { error: 'Failed to generate resume review' },
      { status: 500 }
    );
  }
}
