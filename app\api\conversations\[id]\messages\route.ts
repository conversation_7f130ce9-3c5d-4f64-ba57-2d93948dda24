import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth';
import connectToDatabase from '@/lib/mongodb';
import Conversation from '@/models/Conversation';
import { generateChatCompletion } from '@/lib/groq';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Add a new message to a conversation and get AI response
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const { content } = await req.json();
    
    // Validate conversation ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid conversation ID' },
        { status: 400 }
      );
    }

    // Validate message content
    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { error: 'Message content is required' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Find the conversation
    const conversation = await Conversation.findOne({
      _id: id,
      userId,
    });

    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // Create a new user message
    const userMessage = {
      id: uuidv4(),
      role: 'user',
      content,
      timestamp: new Date(),
    };

    // Add the user message to the conversation
    conversation.messages.push(userMessage);
    await conversation.save();

    // Prepare messages for the AI
    const messagesForAI = conversation.messages.map((msg: { role: any; content: any; }) => ({
      role: msg.role,
      content: msg.content,
    }));

    // Generate AI response
    const aiResponse = await generateChatCompletion(messagesForAI);

    // Add the AI response to the conversation
    const assistantMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: aiResponse.content,
      timestamp: new Date(),
    };

    conversation.messages.push(assistantMessage);
    await conversation.save();

    // If this is the first real message, update the conversation title
    if (conversation.messages.length <= 3 && conversation.title === 'New Chat') {
      // Generate a title based on the conversation
      const titleMessages = [
        { role: 'system', content: 'You are a helpful assistant that generates short, descriptive titles (max 6 words) for conversations. Generate a title for this conversation.' },
        { role: 'user', content },
        { role: 'assistant', content: aiResponse.content },
      ];
      
      const titleResponse = await generateChatCompletion(titleMessages);
      
      // Update the conversation title
      conversation.title = titleResponse.content.replace(/"/g, '').trim();
      await conversation.save();
    }

    return NextResponse.json({
      userMessage,
      assistantMessage,
      title: conversation.title,
    });
  } catch (error) {
    console.error('Error processing message:', error);
    return NextResponse.json(
      { error: 'Failed to process message' },
      { status: 500 }
    );
  }
}
