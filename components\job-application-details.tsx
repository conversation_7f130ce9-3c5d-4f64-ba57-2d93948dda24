"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { useProfile } from "../contexts/ProfileContext";
import { Briefcase, Building, MapPin, Calendar, FileText } from "lucide-react";

interface JobApplicationDetailsProps {
  jobId?: string;
}

export default function JobApplicationDetails({ jobId }: JobApplicationDetailsProps) {
  const {
    jobApplications,
    currentJobApplication,
    loadJobApplication,
    updateJobApplication,
  } = useProfile();
  const [isEditing, setIsEditing] = useState(false);

  // Load the job application if jobId is provided and not already loaded
  useEffect(() => {
    if (jobId && (!currentJobApplication || currentJobApplication._id !== jobId)) {
      loadJobApplication(jobId);
    }
  }, [jobId, currentJobApplication, loadJobApplication]);

  // If no job application is loaded, show a message
  if (!currentJobApplication) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <Briefcase className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No Job Application Selected</h3>
        <p className="text-sm text-muted-foreground text-center mt-2">
          Select a job application from your list or create a new one to get started.
        </p>
        <Button className="mt-4">
          Create New Job Application
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">Current Job Role</h3>
        <p className="text-sm text-muted-foreground">
          Details about the position you're applying for
        </p>
      </div>

      <div className="border rounded-lg p-4">
        <div className="text-sm">
          <div className="flex items-start justify-between">
            <div>
              <p className="font-medium text-base">{currentJobApplication.title}</p>
              <div className="flex items-center text-muted-foreground mt-1">
                <Building className="h-3 w-3 mr-1" />
                <p>{currentJobApplication.company}</p>
                {currentJobApplication.location && (
                  <>
                    <span className="mx-1">•</span>
                    <MapPin className="h-3 w-3 mr-1" />
                    <p>{currentJobApplication.location}</p>
                  </>
                )}
              </div>
            </div>
            <div className="px-2 py-1 rounded text-xs bg-muted">
              {currentJobApplication.status}
            </div>
          </div>

          {currentJobApplication.applicationDate && (
            <div className="flex items-center mt-3 text-xs text-muted-foreground">
              <Calendar className="h-3 w-3 mr-1" />
              <p>Applied: {new Date(currentJobApplication.applicationDate).toLocaleDateString()}</p>
            </div>
          )}

          {currentJobApplication.description && (
            <div className="mt-4">
              <p className="font-medium text-xs">DESCRIPTION</p>
              <p className="mt-1 text-xs">{currentJobApplication.description}</p>
            </div>
          )}

          {currentJobApplication.requirements && currentJobApplication.requirements.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-xs">REQUIREMENTS</p>
              <ul className="list-disc list-inside text-xs mt-1">
                {currentJobApplication.requirements.map((req, index) => (
                  <li key={index}>{req}</li>
                ))}
              </ul>
            </div>
          )}

          {currentJobApplication.responsibilities && currentJobApplication.responsibilities.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-xs">RESPONSIBILITIES</p>
              <ul className="list-disc list-inside text-xs mt-1">
                {currentJobApplication.responsibilities.map((resp, index) => (
                  <li key={index}>{resp}</li>
                ))}
              </ul>
            </div>
          )}

          {currentJobApplication.notes && (
            <div className="mt-4">
              <p className="font-medium text-xs">NOTES</p>
              <p className="mt-1 text-xs">{currentJobApplication.notes}</p>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          size="sm"
          onClick={() => setIsEditing(true)}
        >
          Edit Job Details
        </Button>
      </div>
    </div>
  );
}