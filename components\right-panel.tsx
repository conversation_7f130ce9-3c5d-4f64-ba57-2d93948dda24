"use client";

import { useState, useEffect } from "react";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  SheetTitle,
  SheetDescription,
  SheetClose
} from "./ui/sheet";
import { But<PERSON> } from "./ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "./ui/tabs";
import { Separator } from "./ui/separator";
import { ScrollArea } from "./ui/scroll-area";
import { X, FileText, Briefcase, Edit, Plus } from "lucide-react";
import { useProfile } from "../contexts/ProfileContext";
import JobApplicationDetails from "./job-application-details";
import ResumeDetails from "./resume-details";

interface RightPanelProps {
  open: boolean;
  onClose: () => void;
}

export default function RightPanel({ open, onClose }: RightPanelProps) {
  const {
    resumes,
    currentResume,
    jobApplications,
    currentJobApplication,
    fetchResumes,
    fetchJobApplications,
    loadResume,
    loadJobApplication
  } = useProfile();
  const [activeTab, setActiveTab] = useState("resume");

  // Fetch data when panel opens
  useEffect(() => {
    if (open) {
      fetchResumes();
      fetchJobApplications();
    }
  }, [open, fetchResumes, fetchJobApplications]);

  return (
    <Sheet open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <SheetContent className="w-[400px] sm:w-[540px] p-0">
        <div className="h-full flex flex-col">
          <SheetHeader className="p-6 pb-2">
            <div className="flex justify-between items-center">
              <SheetTitle>Job Application Details</SheetTitle>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
            <SheetDescription>
              View and edit your current job application details
            </SheetDescription>
          </SheetHeader>

          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex-1 flex flex-col"
          >
            <div className="px-6">
              <TabsList className="grid grid-cols-3 w-full">
                <TabsTrigger value="resume">
                  <FileText className="mr-2 h-4 w-4" />
                  Resume
                </TabsTrigger>
                <TabsTrigger value="job">
                  <Briefcase className="mr-2 h-4 w-4" />
                  Job Role
                </TabsTrigger>
                <TabsTrigger value="output">
                  <Edit className="mr-2 h-4 w-4" />
                  Output
                </TabsTrigger>
              </TabsList>
            </div>

            <Separator className="my-4" />

            <ScrollArea className="flex-1 px-6">
              <TabsContent value="resume" className="mt-0">
                {/* Use the ResumeDetails component */}
                <ResumeDetails resumeId={currentResume?._id} />
              </TabsContent>

              <TabsContent value="job" className="mt-0">
                {/* Use the JobApplicationDetails component */}
                <JobApplicationDetails jobId={currentJobApplication?._id} />
              </TabsContent>

              <TabsContent value="output" className="mt-0">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Output Format</h3>
                    <p className="text-sm text-muted-foreground">
                      Choose how you want the AI to format its responses
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                      <div className="font-medium">Cover Letter</div>
                      <div className="text-xs text-muted-foreground mt-1">Formal application letter</div>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                      <div className="font-medium">LinkedIn Bio</div>
                      <div className="text-xs text-muted-foreground mt-1">Professional profile summary</div>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                      <div className="font-medium">Q&A Format</div>
                      <div className="text-xs text-muted-foreground mt-1">Interview preparation</div>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                      <div className="font-medium">Custom Prompt</div>
                      <div className="text-xs text-muted-foreground mt-1">Your specific instructions</div>
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  );
}
